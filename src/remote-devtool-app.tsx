import ReactDOM from 'react-dom/client';
import App from './app';
import { initializeWhitelist, cleanupWhitelist } from './utils/whitelist';

function createElement(id: string) {
  const ele = document.createElement('div');
  ele.id = id;
  document.body.append(ele);
}

export const bootstrap = () => {
  console.log('bootstrap');
  return Promise.resolve();
};

let root: ReactDOM.Root | undefined;

// Whitelist change handler for remote devtool
function handleWhitelistChange(isEnabled: boolean) {
  if (!isEnabled && root) {
    // If whitelist check fails, unmount the app
    unmount();
  } else if (isEnabled && !root) {
    // If whitelist check passes and app is not mounted, mount it
    mount();
  }
}

export const mount = async () => {
  return new Promise<void>(async (resolve) => {
    // Initialize whitelist and check if devtool should be enabled
    const isEnabled = await initializeWhitelist(handleWhitelistChange, {
      enableRoutingListener: true,
    });

    if (!isEnabled) {
      console.log('Remote devtool not enabled due to whitelist restrictions');
      resolve();
      return;
    }

    const idRoot = 'remote-devtool';
    let ele = document.getElementById(idRoot);
    if (!ele) {
      createElement(idRoot);
      ele = document.getElementById(idRoot);
    }

    root = ReactDOM.createRoot(ele!);
    root.render(<App />);

    resolve();
  });
};

export const unmount = () => {
  return new Promise<void>((resolve) => {
    root && root.unmount();
    root = undefined;

    // Clean up whitelist event listeners
    cleanupWhitelist();

    resolve();
  });
};
