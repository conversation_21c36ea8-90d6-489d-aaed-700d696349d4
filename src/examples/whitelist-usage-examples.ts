// Examples of how to use the whitelist utility in different scenarios

import { 
  initializeWhitelist, 
  cleanupWhitelist, 
  checkWhitelist,
  checkPathWhitelist,
  checkAppsWhitelist,
  checkUserWhitelist 
} from '../utils/whitelist';

// Example 1: Simple one-time whitelist check
export async function simpleWhitelistCheck(): Promise<boolean> {
  const isEnabled = await checkWhitelist();
  console.log('Whitelist check result:', isEnabled);
  return isEnabled;
}

// Example 2: Check whitelist with specific apps
export async function checkWithApps(apps: string[]): Promise<boolean> {
  const isEnabled = await checkWhitelist(apps);
  console.log('Whitelist check with apps:', apps, 'Result:', isEnabled);
  return isEnabled;
}

// Example 3: Initialize with callback and routing listener
export async function initializeWithCallback(): Promise<void> {
  const handleWhitelistChange = (isEnabled: boolean) => {
    console.log('Whitelist status changed:', isEnabled);
    // Your custom logic here
    if (isEnabled) {
      console.log('✅ Feature enabled');
    } else {
      console.log('❌ Feature disabled');
    }
  };

  await initializeWhitelist(handleWhitelistChange, { enableRoutingListener: true });
}

// Example 4: Initialize without routing listener (for one-time checks)
export async function initializeWithoutRouting(): Promise<boolean> {
  const handleWhitelistChange = (isEnabled: boolean) => {
    console.log('Initial whitelist check:', isEnabled);
  };

  return await initializeWhitelist(handleWhitelistChange, false);
}

// Example 5: Using individual check functions for advanced logic
export async function advancedWhitelistLogic(): Promise<void> {
  // First load the whitelist data
  await checkWhitelist(); // This loads the data

  // Then use individual checks
  const pathAllowed = checkPathWhitelist();
  const userAllowed = checkUserWhitelist();
  const appsAllowed = checkAppsWhitelist(['test-app', 'history']);

  console.log('Path allowed:', pathAllowed);
  console.log('User allowed:', userAllowed);
  console.log('Apps allowed:', appsAllowed);

  // Custom logic based on individual checks
  if (pathAllowed) {
    console.log('Enabled due to path whitelist');
  } else if (userAllowed) {
    console.log('Enabled due to user whitelist');
  } else if (appsAllowed) {
    console.log('Enabled due to apps whitelist');
  } else {
    console.log('Not enabled - no whitelist criteria met');
  }
}

// Example 6: Component-like usage with cleanup
export class WhitelistManager {
  private isInitialized = false;

  async initialize(): Promise<boolean> {
    if (this.isInitialized) return true;

    const handleChange = (isEnabled: boolean) => {
      this.onWhitelistChange(isEnabled);
    };

    const isEnabled = await initializeWhitelist(handleChange, true);
    this.isInitialized = true;
    return isEnabled;
  }

  private onWhitelistChange(isEnabled: boolean): void {
    console.log('WhitelistManager: status changed to', isEnabled);
    // Add your component-specific logic here
  }

  cleanup(): void {
    if (this.isInitialized) {
      cleanupWhitelist();
      this.isInitialized = false;
    }
  }
}

// Example 7: React-like hook pattern (for use in any file)
export function useWhitelistCheck() {
  let isEnabled = false;
  let isLoading = true;

  const checkStatus = async () => {
    isLoading = true;
    isEnabled = await checkWhitelist();
    isLoading = false;
    return { isEnabled, isLoading };
  };

  return { checkStatus };
}

// Example 8: Conditional feature enablement
export async function conditionalFeature(featureName: string): Promise<void> {
  const isEnabled = await checkWhitelist();
  
  if (isEnabled) {
    console.log(`🚀 ${featureName} is enabled`);
    // Enable your feature here
  } else {
    console.log(`🔒 ${featureName} is disabled by whitelist`);
    // Disable or hide your feature here
  }
}
