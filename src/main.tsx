import ReactDOM from 'react-dom/client';
import App from './app';
import { initializeWhitelist } from './utils/whitelist';

// Whitelist change handler for main app
function handleWhitelistChange(isEnabled: boolean) {
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.style.display = isEnabled ? 'block' : 'none';
  }
}

// Initialize the app with whitelist check
async function initializeApp() {
  // Initialize whitelist and check if devtool should be enabled
  const isEnabled = await initializeWhitelist(handleWhitelistChange, { enableRoutingListener: true });

  if (!isEnabled) {
    console.log('Remote devtool not enabled due to whitelist restrictions');
    // Hide the root element
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.style.display = 'none';
    }
    return;
  }

  // Render the app if whitelist check passes
  const root = ReactDOM.createRoot(document.getElementById('root')!);
  root.render(<App />);
}

// Start the app
initializeApp();
