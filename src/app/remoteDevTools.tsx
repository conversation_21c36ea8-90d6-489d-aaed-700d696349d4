import { useCallback, useState } from 'react';
import { createPortal } from 'react-dom';
import { Corners } from '../components/draggable';
import iconDev from '../assets/dev.svg';
import iconPrev from '../assets/prev.svg';

import { loadRemoteDevTool } from '../libs/loadRemoteDevTool.ts';
import { ContactPoints } from './ContactPoints.tsx';
import { DragItem } from '../components/drag-item.tsx';
import { Switch } from '../components/ui/switch.tsx';

const INDICATOR_PADDING = 20;
const INDICATOR_POSITION = 'bottom-left' as Corners;

export const STORAGE_KEY_POSITION = 'zlp-dev-tools-position';
export type DevToolsIndicatorPosition = typeof INDICATOR_POSITION;

export function getInitialPosition() {
  if (
    typeof localStorage !== 'undefined' &&
    localStorage.getItem(STORAGE_KEY_POSITION)
  ) {
    return localStorage.getItem(
      STORAGE_KEY_POSITION,
    ) as DevToolsIndicatorPosition;
  }
  return INDICATOR_POSITION;
}

export function savePosition(position: DevToolsIndicatorPosition) {
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem(STORAGE_KEY_POSITION, position);
  }
}

export function getPositionWithSafeArea(corner: Corners) {
  const padding = INDICATOR_PADDING;

  switch (corner) {
    case 'top-left':
      return { x: padding, y: padding };
    case 'top-right':
      return { x: window.innerWidth - padding - 64, y: padding };
    case 'bottom-left':
      return { x: padding, y: window.innerHeight - padding - 64 };
    case 'bottom-right':
      return {
        x: window.innerWidth - padding - 64,
        y: window.innerHeight - padding - 64,
      };
    default:
      return { x: padding, y: window.innerHeight - padding - 64 };
  }
}
const OVERLAYS = {
  Root: 'root',
  Turbo: 'turbo',
  Route: 'route',
  Preferences: 'preferences',
  SegmentExplorer: 'segment-explorer',
} as const;

export type Overlays = (typeof OVERLAYS)[keyof typeof OVERLAYS];

export function RemoteDevTools() {
  const [remoteLoaded, setRemoteLoaded] = useState(false);
  const [position, setPosition] = useState(getInitialPosition());
  const [open, setOpen] = useState(false);
  const handlePositionChange = useCallback(
    (newPosition: DevToolsIndicatorPosition) => {
      setPosition(newPosition);
      savePosition(newPosition);
    },
    [],
  );

  const onRemoteDevtools = useCallback(() => {
    if (remoteLoaded) {
      console.info('Remote Already loaded!');
      return;
    }
    try {
      loadRemoteDevTool().then(() => {
        setRemoteLoaded(true);
        console.info('Enabled remote devtools');
      });
    } catch {
      remoteLoaded && setRemoteLoaded(false);
      console.error('init remote devtools failed');
    }
  }, [remoteLoaded]);

  console.log('render');

  return createPortal(
    <div id="remote-dev-tools-root" className="remote-dev-tools-root">
      <div className="fixed left-0 top-0 z-[999999999999] h-0 w-0">
        <DragItem
          initialPosition={getPositionWithSafeArea(position)}
          onPositionChange={handlePositionChange}
        >
          <div className="flex flex-col bg-gray-100 rounded-xl ring-1 ring-slate-300">
            <button
              onClick={() => setOpen((open) => !open)}
              className="inline-flex w-10 h-10 p-1 overflow-hidden"
            >
              <img
                draggable="false"
                className="overflow-hidden rounded-full"
                src={open ? iconPrev : iconDev}
                alt="zlp"
              />
            </button>
            {open ? (
              <div className="flex flex-col w-full gap-2 p-3 bg-blue-100 rounded-xl">
                <div className="flex flex-row items-center justify-start gap-2">
                  <Switch
                    checked={remoteLoaded}
                    disabled={remoteLoaded}
                    onCheckedChange={onRemoteDevtools}
                  />
                  <label>Enable</label>
                </div>
                <a
                  target="_blank"
                  className="font-semibold"
                  href="https://dev-zui.zalopay.vn/dev-tools"
                >
                  Inspect Here
                </a>
                <ContactPoints />
              </div>
            ) : null}
          </div>

          {/* <Popover>
            <PopoverTrigger asChild>
              <button className="absolute z-10 inline-flex p-1 overflow-hidden bg-slate-200 rounded-xl opacity-45 ring-1 ring-slate-300">
                <img
                  className="h-[32px] w-[32px] overflow-hidden rounded-full"
                  src={iconDev}
                  alt="zlp"
                />
              </button>
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col w-full gap-2 px-2 py-4 bg-green-100 rounded-xl">
                
                <Switch
                      checked={remoteLoaded}
                      disabled={remoteLoaded}
                      onCheckedChange={onRemoteDevtools}
                    />
                <a
                  target="_blank"
                  className="font-semibold"
                  href="https://dev-zui.zalopay.vn/dev-tools"
                >
                  Inspect Here
                </a>
                <ContactPoints />
              </div>
            </PopoverContent>
          </Popover> */}
          {/* <Button
            onClick={() => setOpen((open) => !open)}
            className="absolute z-10 inline-flex p-1 overflow-hidden bg-red-100 rounded-xl opacity-45 ring-1 ring-slate-300"
          >
            <img
              className="h-[32px] w-[32px] overflow-hidden rounded-full"
              src={iconDev}
              alt="zlp"
            />
          </Button>

          {open ? (
            <Popover className="remote-dev-tools-root !z-[999999999999]">
              <div className="!z-[999999999999] origin-top-left overflow-auto rounded-xl bg-yellow-100 p-4 px-4 shadow-lg ring-1 ring-black ring-opacity-5 fill-mode-forwards entering:animate-in entering:fade-in entering:zoom-in-95 exiting:animate-out exiting:fade-out exiting:zoom-out-95">
                <div className="flex flex-col w-full gap-2">
                  <Switch
                    isSelected={remoteLoaded}
                    onChange={onRemoteDevtools}
                    isDisabled={remoteLoaded}
                    className="flex items-center gap-2"
                  >
                    Remote Devtools
                  </Switch>
                  <a
                    target="_blank"
                    className="font-semibold"
                    href="https://dev-zui.zalopay.vn/dev-tools"
                  >
                    Inspect Here
                  </a>
                  <ContactPoints />
                </div>
              </div>
            </Popover>
          ) : null} */}
        </DragItem>
      </div>
    </div>,
    document.body,
  );
}
