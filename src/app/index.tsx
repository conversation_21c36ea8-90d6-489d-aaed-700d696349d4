import React from 'react';
import { RemoteDevTools } from './remoteDevTools';
import '../styles/globals.css';

declare global {
  interface Window {
    eruda?: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      init: (...agrs: unknown | any) => void;
    };
  }
}

function App() {
  return (
    <React.StrictMode>
      <RemoteDevTools />
    </React.StrictMode>
  );
}

App.displayName = 'RemoteDevTools';

export default App;
