declare global {
  interface Window {
    __USER_INFO__?: string;
  }
}

// Whitelist configuration
const cacheKey = window.crypto.getRandomValues(new Int16Array(1))[0] || 1;
const WHITE_LIST_URL = `https://simg.zalopay.com.vn/fs/dev-tools/white-list.json?cacheKey=${Math.abs(cacheKey)}`;
const WHITE_LIST_UID = `https://simg.zalopay.com.vn/fs/dev-tools/white-list-uid.json?cacheKey=${Math.abs(cacheKey)}`;

// Whitelist data storage
let whiteList: string[] = [];
let whiteListUIDs: string[] = [];
let whitelistDataLoaded = false;

// Whitelist check functions
function checkPathWhitelist(): boolean {
  const url = window.location.href;
  return whiteList.some((path: string) => url.includes(path));
}

function checkAppsWhitelist(apps: string[]): boolean {
  return apps.some((app) => whiteList.includes(app));
}

function checkUserWhitelist(): boolean {
  const userID = window.__USER_INFO__
    ? JSON.parse(window.__USER_INFO__)?.zalopay_id
    : undefined;
  return userID && whiteListUIDs.some((uid) => uid === String(userID));
}

function checkAllWhitelists(apps: string[] = []): boolean {
  const pathEnabled = checkPathWhitelist();
  const appsEnabled = apps.length > 0 ? checkAppsWhitelist(apps) : false;
  const userEnabled = checkUserWhitelist();

  // Using OR logic as per user preference
  return pathEnabled || appsEnabled || userEnabled;
}

// Load whitelist data from remote URLs
async function loadWhitelistData(): Promise<void> {
  if (whitelistDataLoaded) return;

  try {
    const [whiteListResponse, whiteListUIDResponse] = await Promise.all([
      fetch(WHITE_LIST_URL),
      fetch(WHITE_LIST_UID),
    ]);

    const [whiteListData, whiteListUIDData] = await Promise.all([
      whiteListResponse.json(),
      whiteListUIDResponse.json(),
    ]);

    if (whiteListData?.white_list?.length) {
      whiteList = whiteListData.white_list;
    }

    if (whiteListUIDData?.white_list_uid?.length) {
      whiteListUIDs = whiteListUIDData.white_list_uid;
    }

    whitelistDataLoaded = true;
  } catch (error) {
    console.error('Failed to fetch whitelist data:', error);
  }
}

// Routing event listener for single-spa
function createRoutingListener(
  onWhitelistChange: (isEnabled: boolean) => void,
) {
  return (evt: CustomEvent) => {
    const apps = evt.detail?.appsByNewStatus.MOUNTED || [];
    if (apps && apps.length) {
      const isEnabled = checkAllWhitelists(apps);
      onWhitelistChange(isEnabled);
    }
  };
}

// Main whitelist checker function
export async function initializeWhitelist(
  onWhitelistResult: (isEnabled: boolean) => void,
  { enableRoutingListener: true } : { enableRoutingListener?: boolean},
): Promise<boolean> {
  // Load whitelist data first
  await loadWhitelistData();

  // Check if devtool should be enabled
  const isEnabled = checkAllWhitelists();

  // Call the callback with the result
  onWhitelistResult(isEnabled);

  // Set up routing listener if requested
  if (enableRoutingListener) {
    const routingListener = createRoutingListener(onWhitelistResult);
    window.addEventListener(
      'single-spa:routing-event',
      routingListener as EventListener,
    );

    // Return cleanup function
    const cleanup = () => {
      window.removeEventListener(
        'single-spa:routing-event',
        routingListener as EventListener,
      );
    };

    // Store cleanup function for later use
    (window as any).__whitelistCleanup = cleanup;
  }

  return isEnabled;
}

// Cleanup function for removing event listeners
export function cleanupWhitelist(): void {
  if ((window as any).__whitelistCleanup) {
    (window as any).__whitelistCleanup();
    delete (window as any).__whitelistCleanup;
  }
}

// Direct whitelist check (for cases where you just need a quick check)
export async function checkWhitelist(apps: string[] = []): Promise<boolean> {
  await loadWhitelistData();
  return checkAllWhitelists(apps);
}

// Export individual check functions for advanced usage
export {
  checkPathWhitelist,
  checkAppsWhitelist,
  checkUserWhitelist,
  checkAllWhitelists,
  loadWhitelistData,
};
