{"name": "remote-devtool", "version": "0.0.0-dev-r5", "private": true, "type": "module", "description": "", "keywords": [], "author": "Duy Đoàn Nguyễn Hữu", "license": "ISC", "scripts": {"dev": "vite", "wp-build": "vite build", "preview": "vite preview"}, "browserslist": {"production": ["Android >= 4", "iOS >= 8", "ChromeAndroid >= 1"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-switch": "^1.2.5", "@react-spring/web": "^9.7.3", "@use-gesture/react": "^10.3.1", "clsx": "^2.1.1", "react": "^18.2.0", "react-aria-components": "^1.2.1", "react-dom": "^18.2.0", "react-router-dom": "^6.3.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.8", "@types/draggabilly": "^2.1.3", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.0", "sass": "^1.54.5", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "^1.1.3", "typescript": "^5.4.5", "vite": "^5.2.12"}}